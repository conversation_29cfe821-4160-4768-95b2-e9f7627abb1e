apiVersion: apps/v1
kind: Deployment
metadata:
  name: sigma-protocol
  namespace: default
  labels:
    app: sigma-protocol
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: sigma-protocol
  template:
    metadata:
      labels:
        app: sigma-protocol
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: sigma-protocol
        image: sigma-protocol:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        - containerPort: 8080
          name: websocket
          protocol: TCP
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: sigma-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: sigma-secrets
              key: redis-url
        - name: BINANCE_API_KEY
          valueFrom:
            secretKeyRef:
              name: sigma-secrets
              key: binance-api-key
        - name: BINANCE_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: sigma-secrets
              key: binance-secret-key
        - name: ETHEREUM_RPC_URL
          valueFrom:
            secretKeyRef:
              name: sigma-secrets
              key: ethereum-rpc-url
        - name: PRIVATE_KEY
          valueFrom:
            secretKeyRef:
              name: sigma-secrets
              key: ethereum-private-key
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: sigma-secrets
              key: jwt-secret-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: logs-volume
          mountPath: /app/logs
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: config-volume
        configMap:
          name: sigma-config
      - name: logs-volume
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      securityContext:
        fsGroup: 1000

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: sigma-config
  namespace: default
data:
  production.yml: |
    # Sigma Protocol Production Configuration
    
    # Trading Configuration
    trading:
      max_position_size: 10000
      risk_limit: 0.02
      stop_loss_percentage: 0.05
      take_profit_percentage: 0.10
      
    # Strategy Configuration
    strategies:
      perpetual_arbitrage:
        enabled: true
        min_funding_rate: 0.01
        rate_limit_requests: 10
        rate_limit_window: 60
        
      dex_amm:
        enabled: true
        min_profit_threshold: 0.005
        max_slippage: 0.01
        max_gas_price: 100
        
      statistical_arbitrage:
        enabled: true
        lookback_period: 60
        entry_zscore: 2.0
        exit_zscore: 0.5
        min_correlation: 0.7
        
      mev_integration:
        enabled: true
        min_profit_threshold: 0.01
        sandwich_protection: true
        flashloan_enabled: true
    
    # Risk Management
    risk:
      target_sharpe_ratio: 1.5
      max_drawdown: 0.10
      target_beta: 0.1
      var_confidence_level: 0.95
      
    # Monitoring
    monitoring:
      prometheus_enabled: true
      log_level: INFO
      metrics_port: 8000
      
    # Rate Limiting
    rate_limiting:
      enabled: true
      requests_per_minute: 100
      burst_size: 20

---
apiVersion: v1
kind: Secret
metadata:
  name: sigma-secrets
  namespace: default
type: Opaque
stringData:
  database-url: "***********************************************/sigma_protocol"
  redis-url: "redis://redis:6379"
  binance-api-key: "CHANGE_ME"
  binance-secret-key: "CHANGE_ME"
  ethereum-rpc-url: "https://mainnet.infura.io/v3/CHANGE_ME"
  ethereum-private-key: "CHANGE_ME"
  jwt-secret-key: "CHANGE_ME"

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: sigma-protocol-hpa
  namespace: default
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: sigma-protocol
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: sigma-protocol-pdb
  namespace: default
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: sigma-protocol
