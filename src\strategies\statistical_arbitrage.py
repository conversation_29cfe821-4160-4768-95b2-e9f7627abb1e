"""
Statistical Arbitrage Strategy

This strategy implements various statistical arbitrage techniques:
1. Pairs trading using cointegration and error correction models
2. Mean reversion strategies on crypto sector ETFs
3. Momentum strategies with market-neutral implementation
4. Factor model arbitrage using PCA-derived factors
"""

import asyncio
import logging
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from dataclasses import dataclass
from scipy import stats
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

try:
    import sigma_core
except ImportError:
    # Fallback for development
    class sigma_core:
        class Strategy:
            def on_market_data(self, data): pass
            def generate_signals(self): return []
            def get_name(self): return ""
            def is_enabled(self): return True
            def set_enabled(self, enabled): pass
        class Order:
            def __init__(self):
                self.id = ""
                self.symbol = ""
                self.side = ""
                self.quantity = 0.0
                self.price = 0.0
                self.type = ""
                self.exchange = ""

@dataclass
class PairTradingSignal:
    """Pairs trading signal information"""
    symbol1: str
    symbol2: str
    spread: float
    zscore: float
    half_life: float
    confidence: float
    entry_threshold: float
    exit_threshold: float
    
@dataclass
class FactorExposure:
    """Factor exposure for a symbol"""
    symbol: str
    market_factor: float
    size_factor: float
    momentum_factor: float
    volatility_factor: float
    residual_return: float

class StatisticalArbitrageStrategy(sigma_core.Strategy):
    """
    Statistical arbitrage strategy with multiple models and risk controls
    """
    
    def __init__(self, config: Dict):
        super().__init__()
        self.name = "StatisticalArbitrageStrategy"
        self.enabled = config.get('enabled', True)
        self.lookback_period = config.get('lookback_period', 60)  # 60 periods
        self.entry_zscore = config.get('entry_zscore', 2.0)
        self.exit_zscore = config.get('exit_zscore', 0.5)
        self.min_half_life = config.get('min_half_life', 5)  # Minimum mean reversion speed
        self.max_half_life = config.get('max_half_life', 30)
        self.min_correlation = config.get('min_correlation', 0.7)
        self.max_position_size = config.get('max_position_size', 1000)
        
        # Data storage
        self.price_data: Dict[str, List[float]] = {}
        self.return_data: Dict[str, List[float]] = {}
        self.volume_data: Dict[str, List[float]] = {}
        self.timestamp_data: Dict[str, List[datetime]] = {}
        
        # Statistical models
        self.cointegration_pairs: Dict[str, Dict] = {}
        self.factor_model: Optional[PCA] = None
        self.factor_loadings: Dict[str, FactorExposure] = {}
        self.scaler = StandardScaler()
        
        # Active positions and signals
        self.active_pairs: Dict[str, PairTradingSignal] = {}
        self.momentum_signals: Dict[str, float] = {}
        self.mean_reversion_signals: Dict[str, float] = {}
        
        # Performance tracking
        self.successful_pairs = 0
        self.failed_pairs = 0
        self.total_trades = 0
        self.sharpe_ratio = 0.0
        
        # Rate limiting
        self.last_model_update = datetime.now()
        self.model_update_interval = timedelta(minutes=5)
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Initialized {self.name} with lookback period {self.lookback_period}")
    
    def get_name(self) -> str:
        return self.name
    
    def is_enabled(self) -> bool:
        return self.enabled
    
    def set_enabled(self, enabled: bool):
        self.enabled = enabled
        self.logger.info(f"Strategy {self.name} {'enabled' if enabled else 'disabled'}")
    
    def on_market_data(self, data: sigma_core.MarketData):
        """Process incoming market data and update statistical models"""
        if not self.enabled:
            return
        
        symbol = data.symbol
        current_time = datetime.now()
        
        # Initialize data structures
        if symbol not in self.price_data:
            self.price_data[symbol] = []
            self.return_data[symbol] = []
            self.volume_data[symbol] = []
            self.timestamp_data[symbol] = []
        
        # Store new data
        self.price_data[symbol].append(data.last_price)
        self.volume_data[symbol].append(data.volume)
        self.timestamp_data[symbol].append(current_time)
        
        # Calculate returns
        if len(self.price_data[symbol]) > 1:
            prev_price = self.price_data[symbol][-2]
            if prev_price > 0:
                return_val = (data.last_price - prev_price) / prev_price
                self.return_data[symbol].append(return_val)
        
        # Maintain rolling window
        if len(self.price_data[symbol]) > self.lookback_period:
            self.price_data[symbol] = self.price_data[symbol][-self.lookback_period:]
            self.return_data[symbol] = self.return_data[symbol][-self.lookback_period:]
            self.volume_data[symbol] = self.volume_data[symbol][-self.lookback_period:]
            self.timestamp_data[symbol] = self.timestamp_data[symbol][-self.lookback_period:]
        
        # Update models periodically
        if current_time - self.last_model_update >= self.model_update_interval:
            self._update_statistical_models()
            self.last_model_update = current_time
    
    def _update_statistical_models(self):
        """Update cointegration pairs and factor models"""
        try:
            # Update cointegration pairs
            self._update_cointegration_pairs()
            
            # Update factor model
            self._update_factor_model()
            
            # Update momentum and mean reversion signals
            self._update_momentum_signals()
            self._update_mean_reversion_signals()
            
        except Exception as e:
            self.logger.error(f"Error updating statistical models: {e}")
    
    def _update_cointegration_pairs(self):
        """Find and update cointegrated pairs"""
        symbols = list(self.price_data.keys())
        
        # Need at least 2 symbols with sufficient data
        valid_symbols = [s for s in symbols 
                        if len(self.price_data[s]) >= self.lookback_period // 2]
        
        if len(valid_symbols) < 2:
            return
        
        # Test all pairs for cointegration
        for i in range(len(valid_symbols)):
            for j in range(i + 1, len(valid_symbols)):
                symbol1, symbol2 = valid_symbols[i], valid_symbols[j]
                pair_key = f"{symbol1}_{symbol2}"
                
                # Get price series
                prices1 = np.array(self.price_data[symbol1])
                prices2 = np.array(self.price_data[symbol2])
                
                # Ensure same length
                min_len = min(len(prices1), len(prices2))
                if min_len < 20:  # Need minimum data
                    continue
                
                prices1 = prices1[-min_len:]
                prices2 = prices2[-min_len:]
                
                # Test cointegration
                coint_result = self._test_cointegration(prices1, prices2)
                
                if coint_result['is_cointegrated']:
                    # Calculate spread and statistics
                    spread = prices1 - coint_result['hedge_ratio'] * prices2
                    zscore = self._calculate_zscore(spread)
                    half_life = self._calculate_half_life(spread)
                    
                    if (self.min_half_life <= half_life <= self.max_half_life and
                        abs(zscore[-1]) > 0.5):  # Current spread is meaningful
                        
                        signal = PairTradingSignal(
                            symbol1=symbol1,
                            symbol2=symbol2,
                            spread=spread[-1],
                            zscore=zscore[-1],
                            half_life=half_life,
                            confidence=coint_result['confidence'],
                            entry_threshold=self.entry_zscore,
                            exit_threshold=self.exit_zscore
                        )
                        
                        self.cointegration_pairs[pair_key] = {
                            'signal': signal,
                            'hedge_ratio': coint_result['hedge_ratio'],
                            'spread_series': spread,
                            'zscore_series': zscore
                        }
    
    def _test_cointegration(self, prices1: np.ndarray, prices2: np.ndarray) -> Dict:
        """Test for cointegration between two price series"""
        try:
            # Calculate correlation first
            correlation = np.corrcoef(prices1, prices2)[0, 1]
            
            if abs(correlation) < self.min_correlation:
                return {'is_cointegrated': False, 'confidence': 0.0}
            
            # Estimate hedge ratio using OLS
            X = np.column_stack([np.ones(len(prices2)), prices2])
            coeffs = np.linalg.lstsq(X, prices1, rcond=None)[0]
            hedge_ratio = coeffs[1]
            
            # Calculate residuals (spread)
            spread = prices1 - hedge_ratio * prices2
            
            # Augmented Dickey-Fuller test on spread
            # Simplified implementation - in practice use statsmodels
            spread_diff = np.diff(spread)
            spread_lag = spread[:-1]
            
            if len(spread_diff) > 10:
                # Simple regression: diff(spread) = alpha + beta * spread_lag + error
                X_adf = np.column_stack([np.ones(len(spread_lag)), spread_lag])
                try:
                    adf_coeffs = np.linalg.lstsq(X_adf, spread_diff, rcond=None)[0]
                    t_stat = adf_coeffs[1] / (np.std(spread_diff) / np.sqrt(len(spread_diff)))
                    
                    # Critical values (simplified)
                    critical_value = -2.86  # 5% significance level
                    is_cointegrated = t_stat < critical_value
                    confidence = max(0, min(1, abs(t_stat) / 5))  # Normalized confidence
                    
                    return {
                        'is_cointegrated': is_cointegrated,
                        'hedge_ratio': hedge_ratio,
                        'confidence': confidence,
                        't_statistic': t_stat
                    }
                except np.linalg.LinAlgError:
                    return {'is_cointegrated': False, 'confidence': 0.0}
            
            return {'is_cointegrated': False, 'confidence': 0.0}
            
        except Exception as e:
            self.logger.error(f"Error in cointegration test: {e}")
            return {'is_cointegrated': False, 'confidence': 0.0}
    
    def _calculate_zscore(self, series: np.ndarray, window: int = 20) -> np.ndarray:
        """Calculate rolling z-score of a series"""
        if len(series) < window:
            return np.zeros_like(series)
        
        zscores = np.zeros_like(series)
        
        for i in range(window, len(series)):
            window_data = series[i-window:i]
            mean = np.mean(window_data)
            std = np.std(window_data)
            
            if std > 0:
                zscores[i] = (series[i] - mean) / std
        
        return zscores
    
    def _calculate_half_life(self, series: np.ndarray) -> float:
        """Calculate half-life of mean reversion"""
        try:
            # Ornstein-Uhlenbeck process: dx = -lambda * x * dt + sigma * dW
            # Half-life = ln(2) / lambda
            
            if len(series) < 10:
                return float('inf')
            
            x = series[:-1]
            dx = np.diff(series)
            
            # Regression: dx = alpha + beta * x + error
            # beta = -lambda
            X = np.column_stack([np.ones(len(x)), x])
            
            try:
                coeffs = np.linalg.lstsq(X, dx, rcond=None)[0]
                lambda_param = -coeffs[1]
                
                if lambda_param > 0:
                    half_life = np.log(2) / lambda_param
                    return min(half_life, 100)  # Cap at 100 periods
                else:
                    return float('inf')
                    
            except np.linalg.LinAlgError:
                return float('inf')
                
        except Exception as e:
            self.logger.error(f"Error calculating half-life: {e}")
            return float('inf')
    
    def _update_factor_model(self):
        """Update factor model using PCA"""
        try:
            # Get return matrix
            symbols = list(self.return_data.keys())
            valid_symbols = [s for s in symbols if len(self.return_data[s]) >= 20]
            
            if len(valid_symbols) < 3:
                return
            
            # Create return matrix
            min_length = min(len(self.return_data[s]) for s in valid_symbols)
            return_matrix = np.array([
                self.return_data[s][-min_length:] for s in valid_symbols
            ]).T
            
            # Standardize returns
            return_matrix_scaled = self.scaler.fit_transform(return_matrix)
            
            # Fit PCA
            self.factor_model = PCA(n_components=min(4, len(valid_symbols)))
            factors = self.factor_model.fit_transform(return_matrix_scaled)
            
            # Calculate factor loadings for each symbol
            for i, symbol in enumerate(valid_symbols):
                loadings = self.factor_model.components_[:, i]
                
                # Assign factor interpretations
                self.factor_loadings[symbol] = FactorExposure(
                    symbol=symbol,
                    market_factor=loadings[0] if len(loadings) > 0 else 0,
                    size_factor=loadings[1] if len(loadings) > 1 else 0,
                    momentum_factor=loadings[2] if len(loadings) > 2 else 0,
                    volatility_factor=loadings[3] if len(loadings) > 3 else 0,
                    residual_return=np.std(return_matrix_scaled[:, i] - 
                                         factors @ loadings)
                )
                
        except Exception as e:
            self.logger.error(f"Error updating factor model: {e}")
    
    def _update_momentum_signals(self):
        """Update momentum signals for each symbol"""
        for symbol, prices in self.price_data.items():
            if len(prices) >= 20:
                # Calculate momentum (price change over lookback period)
                short_ma = np.mean(prices[-5:])  # 5-period MA
                long_ma = np.mean(prices[-20:])  # 20-period MA
                
                momentum = (short_ma - long_ma) / long_ma if long_ma > 0 else 0
                self.momentum_signals[symbol] = momentum
    
    def _update_mean_reversion_signals(self):
        """Update mean reversion signals"""
        for symbol, prices in self.price_data.items():
            if len(prices) >= 20:
                # Calculate deviation from long-term mean
                long_term_mean = np.mean(prices)
                current_price = prices[-1]
                
                deviation = (current_price - long_term_mean) / long_term_mean
                self.mean_reversion_signals[symbol] = -deviation  # Negative for mean reversion
    
    def generate_signals(self) -> List[sigma_core.Order]:
        """Generate trading signals based on statistical models"""
        if not self.enabled:
            return []
        
        orders = []
        
        # 1. Pairs trading signals
        orders.extend(self._generate_pairs_trading_signals())
        
        # 2. Factor model arbitrage signals
        orders.extend(self._generate_factor_arbitrage_signals())
        
        # 3. Momentum signals (market-neutral)
        orders.extend(self._generate_momentum_signals())
        
        # 4. Mean reversion signals
        orders.extend(self._generate_mean_reversion_signals())
        
        return orders
    
    def _generate_pairs_trading_signals(self) -> List[sigma_core.Order]:
        """Generate pairs trading orders"""
        orders = []
        
        for pair_key, pair_data in self.cointegration_pairs.items():
            signal = pair_data['signal']
            hedge_ratio = pair_data['hedge_ratio']
            
            # Check if we should enter or exit
            if abs(signal.zscore) >= signal.entry_threshold:
                # Entry signal
                quantity = min(100, self.max_position_size * 0.1)
                
                if signal.zscore > 0:
                    # Spread is high - sell symbol1, buy symbol2
                    order1 = sigma_core.Order()
                    order1.id = f"PAIR_SELL_{signal.symbol1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    order1.symbol = signal.symbol1
                    order1.side = "sell"
                    order1.quantity = quantity
                    order1.type = "market"
                    
                    order2 = sigma_core.Order()
                    order2.id = f"PAIR_BUY_{signal.symbol2}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    order2.symbol = signal.symbol2
                    order2.side = "buy"
                    order2.quantity = quantity * hedge_ratio
                    order2.type = "market"
                    
                    orders.extend([order1, order2])
                    
                elif signal.zscore < 0:
                    # Spread is low - buy symbol1, sell symbol2
                    order1 = sigma_core.Order()
                    order1.id = f"PAIR_BUY_{signal.symbol1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    order1.symbol = signal.symbol1
                    order1.side = "buy"
                    order1.quantity = quantity
                    order1.type = "market"
                    
                    order2 = sigma_core.Order()
                    order2.id = f"PAIR_SELL_{signal.symbol2}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    order2.symbol = signal.symbol2
                    order2.side = "sell"
                    order2.quantity = quantity * hedge_ratio
                    order2.type = "market"
                    
                    orders.extend([order1, order2])
        
        return orders
    
    def _generate_factor_arbitrage_signals(self) -> List[sigma_core.Order]:
        """Generate factor model arbitrage signals"""
        orders = []
        
        if not self.factor_loadings:
            return orders
        
        # Find symbols with extreme factor exposures
        for symbol, exposure in self.factor_loadings.items():
            # Look for high residual returns (alpha opportunities)
            if exposure.residual_return > 0.02:  # 2% threshold
                order = sigma_core.Order()
                order.id = f"FACTOR_ALPHA_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                order.symbol = symbol
                order.side = "buy"  # Buy high alpha
                order.quantity = 50
                order.type = "market"
                
                orders.append(order)
        
        return orders
    
    def _generate_momentum_signals(self) -> List[sigma_core.Order]:
        """Generate market-neutral momentum signals"""
        orders = []
        
        if len(self.momentum_signals) < 2:
            return orders
        
        # Sort by momentum
        sorted_momentum = sorted(self.momentum_signals.items(), 
                               key=lambda x: x[1], reverse=True)
        
        # Long top momentum, short bottom momentum (market neutral)
        n_positions = min(2, len(sorted_momentum) // 2)
        
        for i in range(n_positions):
            # Long high momentum
            symbol_long = sorted_momentum[i][0]
            order_long = sigma_core.Order()
            order_long.id = f"MOM_LONG_{symbol_long}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            order_long.symbol = symbol_long
            order_long.side = "buy"
            order_long.quantity = 25
            order_long.type = "market"
            
            # Short low momentum
            symbol_short = sorted_momentum[-(i+1)][0]
            order_short = sigma_core.Order()
            order_short.id = f"MOM_SHORT_{symbol_short}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            order_short.symbol = symbol_short
            order_short.side = "sell"
            order_short.quantity = 25
            order_short.type = "market"
            
            orders.extend([order_long, order_short])
        
        return orders
    
    def _generate_mean_reversion_signals(self) -> List[sigma_core.Order]:
        """Generate mean reversion signals"""
        orders = []
        
        for symbol, signal in self.mean_reversion_signals.items():
            if abs(signal) > 0.05:  # 5% deviation threshold
                order = sigma_core.Order()
                order.id = f"MR_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                order.symbol = symbol
                order.side = "buy" if signal > 0 else "sell"
                order.quantity = 30
                order.type = "limit"
                
                # Set price slightly better than market for mean reversion
                if symbol in self.price_data and self.price_data[symbol]:
                    current_price = self.price_data[symbol][-1]
                    if order.side == "buy":
                        order.price = current_price * 0.999  # Slightly below market
                    else:
                        order.price = current_price * 1.001  # Slightly above market
                
                orders.append(order)
        
        return orders
    
    def get_performance_metrics(self) -> Dict:
        """Get strategy performance metrics"""
        total_pairs = self.successful_pairs + self.failed_pairs
        pair_success_rate = (self.successful_pairs / total_pairs 
                           if total_pairs > 0 else 0)
        
        return {
            'strategy_name': self.name,
            'active_pairs': len(self.cointegration_pairs),
            'successful_pairs': self.successful_pairs,
            'failed_pairs': self.failed_pairs,
            'pair_success_rate': pair_success_rate,
            'total_trades': self.total_trades,
            'sharpe_ratio': self.sharpe_ratio,
            'factor_model_components': (self.factor_model.n_components_ 
                                      if self.factor_model else 0),
            'tracked_symbols': len(self.price_data),
            'enabled': self.enabled
        }
