"""
Sigma Protocol Trading Strategies

This module contains various market-neutral trading strategies including:
- Perpetual futures arbitrage
- DEX AMM strategies  
- Statistical arbitrage
- MEV integration

All strategies implement the Strategy interface and are designed to be
market-neutral with minimal correlation to overall market movements.
"""

from .perpetual_arbitrage import PerpetualArbitrageStrategy
from .dex_amm import DEXAMMStrategy
from .statistical_arbitrage import StatisticalArbitrageStrategy
from .mev_integration import MEVIntegrationStrategy

__all__ = [
    'PerpetualArbitrageStrategy',
    'DEXAMMStrategy', 
    'StatisticalArbitrageStrategy',
    'MEVIntegrationStrategy'
]

__version__ = '1.0.0'
__author__ = '<PERSON>'
