"""
Sigma Protocol FastAPI Application

Main FastAPI application with comprehensive middleware, monitoring,
and cloud deployment support for Netlify, AWS, and GCP.
"""

import os
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any
import asyncio

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
from prometheus_client import Counter, Histogram, generate_latest
import structlog

from .routes import router
from .middleware import (
    RateLimitMiddleware,
    SEOMiddleware,
    GeoLocationMiddleware,
    SecurityMiddleware
)
from ..core import TradingEngine
from ..strategies import (
    PerpetualArbitrageStrategy,
    DEXAMMStrategy,
    StatisticalArbitrageStrategy,
    MEVIntegrationStrategy
)

# Metrics
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

class SigmaProtocolApp:
    """Main application class with trading engine integration"""
    
    def __init__(self):
        self.trading_engine = None
        self.strategies = {}
        self.app_config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load application configuration"""
        return {
            'debug': os.getenv('DEBUG', 'false').lower() == 'true',
            'environment': os.getenv('ENVIRONMENT', 'development'),
            'rate_limit_enabled': os.getenv('RATE_LIMIT_ENABLED', 'true').lower() == 'true',
            'seo_enabled': os.getenv('SEO_ENABLED', 'true').lower() == 'true',
            'cors_origins': os.getenv('CORS_ORIGINS', '*').split(','),
            'max_position_size': float(os.getenv('MAX_POSITION_SIZE', '10000')),
            'risk_limit': float(os.getenv('RISK_LIMIT', '0.02')),
        }
    
    async def initialize_trading_engine(self):
        """Initialize the trading engine and strategies"""
        try:
            # Initialize trading engine
            self.trading_engine = TradingEngine()
            
            # Initialize strategies
            strategy_configs = {
                'perpetual_arbitrage': {
                    'enabled': os.getenv('PERPETUAL_ARBITRAGE_ENABLED', 'true').lower() == 'true',
                    'min_funding_rate': 0.01,
                    'rate_limit_requests': 10,
                    'rate_limit_window': 60
                },
                'dex_amm': {
                    'enabled': os.getenv('DEX_AMM_ENABLED', 'true').lower() == 'true',
                    'ethereum_rpc_url': os.getenv('ETHEREUM_RPC_URL', ''),
                    'private_key': os.getenv('PRIVATE_KEY', ''),
                    'min_profit_threshold': 0.005
                },
                'statistical_arbitrage': {
                    'enabled': os.getenv('STATISTICAL_ARBITRAGE_ENABLED', 'true').lower() == 'true',
                    'lookback_period': 60,
                    'entry_zscore': 2.0,
                    'exit_zscore': 0.5
                },
                'mev_integration': {
                    'enabled': os.getenv('MEV_INTEGRATION_ENABLED', 'true').lower() == 'true',
                    'ethereum_rpc_url': os.getenv('ETHEREUM_RPC_URL', ''),
                    'private_key': os.getenv('PRIVATE_KEY', ''),
                    'sandwich_protection': True
                }
            }
            
            # Create strategy instances
            self.strategies['perpetual_arbitrage'] = PerpetualArbitrageStrategy(
                strategy_configs['perpetual_arbitrage']
            )
            self.strategies['dex_amm'] = DEXAMMStrategy(
                strategy_configs['dex_amm']
            )
            self.strategies['statistical_arbitrage'] = StatisticalArbitrageStrategy(
                strategy_configs['statistical_arbitrage']
            )
            self.strategies['mev_integration'] = MEVIntegrationStrategy(
                strategy_configs['mev_integration']
            )
            
            # Add strategies to trading engine
            for strategy in self.strategies.values():
                self.trading_engine.add_strategy(strategy)
            
            # Start trading engine
            self.trading_engine.start()
            
            logger.info("Trading engine initialized successfully", 
                       strategies=len(self.strategies))
            
        except Exception as e:
            logger.error("Failed to initialize trading engine", error=str(e))
            raise
    
    async def shutdown_trading_engine(self):
        """Shutdown the trading engine"""
        try:
            if self.trading_engine:
                self.trading_engine.stop()
                logger.info("Trading engine shutdown successfully")
        except Exception as e:
            logger.error("Error shutting down trading engine", error=str(e))

# Global app instance
sigma_app = SigmaProtocolApp()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Sigma Protocol application")
    await sigma_app.initialize_trading_engine()
    
    yield
    
    # Shutdown
    logger.info("Shutting down Sigma Protocol application")
    await sigma_app.shutdown_trading_engine()

def create_app() -> FastAPI:
    """Create and configure FastAPI application"""
    
    app = FastAPI(
        title="Sigma Protocol API",
        description="Automated Market-Neutral Strategy Suite",
        version="1.0.0",
        docs_url="/docs" if sigma_app.app_config['debug'] else None,
        redoc_url="/redoc" if sigma_app.app_config['debug'] else None,
        lifespan=lifespan
    )
    
    # Add middleware
    setup_middleware(app)
    
    # Add routes
    app.include_router(router, prefix="/api/v1")
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint for load balancers"""
        return {
            "status": "healthy",
            "environment": sigma_app.app_config['environment'],
            "trading_engine_running": (
                sigma_app.trading_engine.is_running() 
                if sigma_app.trading_engine else False
            ),
            "active_strategies": len(sigma_app.strategies)
        }
    
    # Metrics endpoint for Prometheus
    @app.get("/metrics")
    async def metrics():
        """Prometheus metrics endpoint"""
        return generate_latest()
    
    # Root endpoint with SEO optimization
    @app.get("/")
    async def root(request: Request):
        """Root endpoint with SEO-optimized response"""
        user_agent = request.headers.get('user-agent', '')
        accept_language = request.headers.get('accept-language', 'en')
        
        # Basic SEO optimization
        if 'bot' in user_agent.lower() or 'crawler' in user_agent.lower():
            return {
                "name": "Sigma Protocol",
                "description": "Automated Market-Neutral Cryptocurrency Trading System",
                "version": "1.0.0",
                "author": "Hector Ta",
                "keywords": [
                    "cryptocurrency", "trading", "arbitrage", "market-neutral",
                    "defi", "mev", "algorithmic-trading", "quantitative-finance"
                ],
                "features": [
                    "Perpetual Futures Arbitrage",
                    "DEX AMM Strategies", 
                    "Statistical Arbitrage",
                    "MEV Integration",
                    "Risk Management",
                    "Real-time Monitoring"
                ],
                "performance_targets": {
                    "annual_returns": "15-25%",
                    "max_drawdown": "<10%",
                    "sharpe_ratio": ">1.5",
                    "market_correlation": "<0.2"
                }
            }
        
        return {
            "message": "Welcome to Sigma Protocol API",
            "version": "1.0.0",
            "docs": "/docs",
            "health": "/health",
            "metrics": "/metrics"
        }
    
    # Error handlers
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """Handle HTTP exceptions"""
        logger.warning("HTTP exception", 
                      status_code=exc.status_code,
                      detail=exc.detail,
                      path=request.url.path)
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.detail,
                "status_code": exc.status_code,
                "path": request.url.path
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle general exceptions"""
        logger.error("Unhandled exception",
                    error=str(exc),
                    path=request.url.path,
                    exc_info=True)
        
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "status_code": 500,
                "path": request.url.path
            }
        )
    
    # Request logging middleware
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        """Log all requests with metrics"""
        start_time = asyncio.get_event_loop().time()
        
        # Process request
        response = await call_next(request)
        
        # Calculate duration
        duration = asyncio.get_event_loop().time() - start_time
        
        # Update metrics
        REQUEST_COUNT.labels(
            method=request.method,
            endpoint=request.url.path
        ).inc()
        REQUEST_DURATION.observe(duration)
        
        # Log request
        logger.info("Request processed",
                   method=request.method,
                   path=request.url.path,
                   status_code=response.status_code,
                   duration=duration,
                   user_agent=request.headers.get('user-agent', ''),
                   ip=request.client.host if request.client else 'unknown')
        
        return response
    
    return app

def setup_middleware(app: FastAPI):
    """Setup application middleware"""
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=sigma_app.app_config['cors_origins'],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Compression middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Custom middleware
    if sigma_app.app_config['rate_limit_enabled']:
        app.add_middleware(RateLimitMiddleware)
    
    if sigma_app.app_config['seo_enabled']:
        app.add_middleware(SEOMiddleware)
    
    app.add_middleware(GeoLocationMiddleware)
    app.add_middleware(SecurityMiddleware)

# Create app instance
app = create_app()

if __name__ == "__main__":
    # Development server
    uvicorn.run(
        "src.api.app:app",
        host="0.0.0.0",
        port=8000,
        reload=sigma_app.app_config['debug'],
        log_config={
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                },
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stdout",
                },
            },
            "root": {
                "level": "INFO",
                "handlers": ["default"],
            },
        }
    )
