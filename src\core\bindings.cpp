#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/chrono.h>
#include <pybind11/functional.h>
#include "engine.hpp"

namespace py = pybind11;

PYBIND11_MODULE(sigma_core, m) {
    m.doc() = "Sigma Protocol Core Trading Engine";
    
    // MarketData structure
    py::class_<sigma::MarketData>(m, "MarketData")
        .def(py::init<>())
        .def_readwrite("symbol", &sigma::MarketData::symbol)
        .def_readwrite("bid", &sigma::MarketData::bid)
        .def_readwrite("ask", &sigma::MarketData::ask)
        .def_readwrite("last_price", &sigma::MarketData::last_price)
        .def_readwrite("volume", &sigma::MarketData::volume)
        .def_readwrite("timestamp", &sigma::MarketData::timestamp)
        .def_readwrite("exchange", &sigma::MarketData::exchange);
    
    // Order structure
    py::class_<sigma::Order>(m, "Order")
        .def(py::init<>())
        .def_readwrite("id", &sigma::Order::id)
        .def_readwrite("symbol", &sigma::Order::symbol)
        .def_readwrite("side", &sigma::Order::side)
        .def_readwrite("quantity", &sigma::Order::quantity)
        .def_readwrite("price", &sigma::Order::price)
        .def_readwrite("type", &sigma::Order::type)
        .def_readwrite("status", &sigma::Order::status)
        .def_readwrite("exchange", &sigma::Order::exchange)
        .def_readwrite("created_at", &sigma::Order::created_at)
        .def_readwrite("updated_at", &sigma::Order::updated_at);
    
    // Position structure
    py::class_<sigma::Position>(m, "Position")
        .def(py::init<>())
        .def_readwrite("symbol", &sigma::Position::symbol)
        .def_readwrite("quantity", &sigma::Position::quantity)
        .def_readwrite("avg_price", &sigma::Position::avg_price)
        .def_readwrite("unrealized_pnl", &sigma::Position::unrealized_pnl)
        .def_readwrite("realized_pnl", &sigma::Position::realized_pnl)
        .def_readwrite("exchange", &sigma::Position::exchange)
        .def_readwrite("opened_at", &sigma::Position::opened_at);
    
    // Strategy interface
    py::class_<sigma::Strategy>(m, "Strategy")
        .def("on_market_data", &sigma::Strategy::on_market_data)
        .def("generate_signals", &sigma::Strategy::generate_signals)
        .def("get_name", &sigma::Strategy::get_name)
        .def("is_enabled", &sigma::Strategy::is_enabled)
        .def("set_enabled", &sigma::Strategy::set_enabled);
    
    // RiskManager class
    py::class_<sigma::RiskManager>(m, "RiskManager")
        .def(py::init<double, double, double, double>(),
             py::arg("max_pos_size"), py::arg("risk_limit"),
             py::arg("stop_loss"), py::arg("take_profit"))
        .def("validate_order", &sigma::RiskManager::validate_order)
        .def("update_position", &sigma::RiskManager::update_position)
        .def("calculate_portfolio_beta", &sigma::RiskManager::calculate_portfolio_beta)
        .def("calculate_var", &sigma::RiskManager::calculate_var,
             py::arg("confidence_level") = 0.95)
        .def("generate_risk_orders", &sigma::RiskManager::generate_risk_orders)
        .def("get_position", &sigma::RiskManager::get_position)
        .def("get_all_positions", &sigma::RiskManager::get_all_positions);
    
    // TradingEngine class
    py::class_<sigma::TradingEngine>(m, "TradingEngine")
        .def(py::init<>())
        .def("start", &sigma::TradingEngine::start)
        .def("stop", &sigma::TradingEngine::stop)
        .def("is_running", &sigma::TradingEngine::is_running)
        .def("feed_market_data", &sigma::TradingEngine::feed_market_data)
        .def("submit_order", &sigma::TradingEngine::submit_order)
        .def("get_pending_orders", &sigma::TradingEngine::get_pending_orders)
        .def("set_risk_parameters", &sigma::TradingEngine::set_risk_parameters)
        .def("get_total_pnl", &sigma::TradingEngine::get_total_pnl)
        .def("get_sharpe_ratio", &sigma::TradingEngine::get_sharpe_ratio)
        .def("get_max_drawdown", &sigma::TradingEngine::get_max_drawdown)
        .def("get_win_rate", &sigma::TradingEngine::get_win_rate)
        .def("can_make_request", &sigma::TradingEngine::can_make_request)
        .def("record_request", &sigma::TradingEngine::record_request)
        .def("generate_order_id", &sigma::TradingEngine::generate_order_id)
        .def("calculate_kelly_fraction", &sigma::TradingEngine::calculate_kelly_fraction)
        .def("calculate_portfolio_weights", &sigma::TradingEngine::calculate_portfolio_weights);
    
    // ArbitrageDetector class
    py::class_<sigma::ArbitrageDetector>(m, "ArbitrageDetector")
        .def(py::init<double>(), py::arg("min_profit") = 0.001)
        .def("update_market_data", &sigma::ArbitrageDetector::update_market_data)
        .def("detect_cross_exchange_arbitrage", 
             &sigma::ArbitrageDetector::detect_cross_exchange_arbitrage)
        .def("detect_triangular_arbitrage", 
             &sigma::ArbitrageDetector::detect_triangular_arbitrage)
        .def("calculate_funding_rate_arbitrage", 
             &sigma::ArbitrageDetector::calculate_funding_rate_arbitrage);
    
    // StatisticalModels class
    py::class_<sigma::StatisticalModels>(m, "StatisticalModels")
        .def_static("calculate_cointegration", 
                   &sigma::StatisticalModels::calculate_cointegration)
        .def_static("calculate_half_life", 
                   &sigma::StatisticalModels::calculate_half_life)
        .def_static("calculate_zscore", 
                   &sigma::StatisticalModels::calculate_zscore)
        .def_static("calculate_volatility", 
                   &sigma::StatisticalModels::calculate_volatility)
        .def_static("calculate_correlation", 
                   &sigma::StatisticalModels::calculate_correlation);
}

// Python Strategy wrapper to allow inheritance
class PyStrategy : public sigma::Strategy {
public:
    using sigma::Strategy::Strategy;
    
    void on_market_data(const sigma::MarketData& data) override {
        PYBIND11_OVERRIDE_PURE(
            void,
            sigma::Strategy,
            on_market_data,
            data
        );
    }
    
    std::vector<sigma::Order> generate_signals() override {
        PYBIND11_OVERRIDE_PURE(
            std::vector<sigma::Order>,
            sigma::Strategy,
            generate_signals,
        );
    }
    
    std::string get_name() const override {
        PYBIND11_OVERRIDE_PURE(
            std::string,
            sigma::Strategy,
            get_name,
        );
    }
    
    bool is_enabled() const override {
        PYBIND11_OVERRIDE_PURE(
            bool,
            sigma::Strategy,
            is_enabled,
        );
    }
    
    void set_enabled(bool enabled) override {
        PYBIND11_OVERRIDE_PURE(
            void,
            sigma::Strategy,
            set_enabled,
            enabled
        );
    }
};

// Add Python strategy wrapper to module
PYBIND11_MODULE(sigma_core, m) {
    // ... previous bindings ...
    
    py::class_<sigma::Strategy, PyStrategy>(m, "Strategy")
        .def(py::init<>())
        .def("on_market_data", &sigma::Strategy::on_market_data)
        .def("generate_signals", &sigma::Strategy::generate_signals)
        .def("get_name", &sigma::Strategy::get_name)
        .def("is_enabled", &sigma::Strategy::is_enabled)
        .def("set_enabled", &sigma::Strategy::set_enabled);
}
