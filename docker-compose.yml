version: '3.8'

services:
  # Redis for caching and coordination
  redis:
    image: redis:7-alpine
    container_name: sigma-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - sigma-network

  # PostgreSQL for data storage
  postgres:
    image: postgres:15-alpine
    container_name: sigma-postgres
    environment:
      POSTGRES_DB: sigma_protocol
      POSTGRES_USER: sigma_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-sigma_password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - sigma-network

  # Main application
  sigma-app:
    build: .
    container_name: sigma-app
    ports:
      - "8000:8000"
      - "8080:8080"
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=postgresql://sigma_user:${POSTGRES_PASSWORD:-sigma_password}@postgres:5432/sigma_protocol
      - ENVIRONMENT=production
      - RATE_LIMIT_ENABLED=true
      - SEO_ENABLED=true
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    networks:
      - sigma-network

  # Prometheus for metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: sigma-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - sigma-network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: sigma-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - sigma-network

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: sigma-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - sigma-app
    restart: unless-stopped
    networks:
      - sigma-network

volumes:
  redis_data:
  postgres_data:
  prometheus_data:
  grafana_data:

networks:
  sigma-network:
    driver: bridge
