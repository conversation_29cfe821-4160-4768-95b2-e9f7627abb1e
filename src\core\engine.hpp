#ifndef SIGMA_ENGINE_HPP
#define SIGMA_ENGINE_HPP

#include <vector>
#include <string>
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>
#include <unordered_map>
#include <queue>

namespace sigma {

// Forward declarations
class Order;
class Position;
class Strategy;
class RiskManager;

// Market data structure
struct MarketData {
    std::string symbol;
    double bid;
    double ask;
    double last_price;
    double volume;
    std::chrono::system_clock::time_point timestamp;
    std::string exchange;
};

// Order structure
struct Order {
    std::string id;
    std::string symbol;
    std::string side; // "buy" or "sell"
    double quantity;
    double price;
    std::string type; // "market", "limit", "stop"
    std::string status; // "pending", "filled", "cancelled"
    std::string exchange;
    std::chrono::system_clock::time_point created_at;
    std::chrono::system_clock::time_point updated_at;
};

// Position structure
struct Position {
    std::string symbol;
    double quantity;
    double avg_price;
    double unrealized_pnl;
    double realized_pnl;
    std::string exchange;
    std::chrono::system_clock::time_point opened_at;
};

// Strategy interface
class Strategy {
public:
    virtual ~Strategy() = default;
    virtual void on_market_data(const MarketData& data) = 0;
    virtual std::vector<Order> generate_signals() = 0;
    virtual std::string get_name() const = 0;
    virtual bool is_enabled() const = 0;
    virtual void set_enabled(bool enabled) = 0;
};

// Risk management class
class RiskManager {
private:
    double max_position_size_;
    double risk_limit_;
    double stop_loss_percentage_;
    double take_profit_percentage_;
    std::mutex positions_mutex_;
    std::unordered_map<std::string, Position> positions_;

public:
    RiskManager(double max_pos_size, double risk_limit, 
                double stop_loss, double take_profit);
    
    bool validate_order(const Order& order);
    void update_position(const std::string& symbol, const Order& order);
    double calculate_portfolio_beta();
    double calculate_var(double confidence_level = 0.95);
    std::vector<Order> generate_risk_orders();
    Position get_position(const std::string& symbol);
    std::vector<Position> get_all_positions();
};

// Main trading engine
class TradingEngine {
private:
    std::atomic<bool> running_;
    std::vector<std::unique_ptr<Strategy>> strategies_;
    std::unique_ptr<RiskManager> risk_manager_;
    std::mutex market_data_mutex_;
    std::mutex orders_mutex_;
    std::queue<MarketData> market_data_queue_;
    std::queue<Order> order_queue_;
    std::thread market_data_thread_;
    std::thread execution_thread_;
    std::thread risk_thread_;
    
    // Performance metrics
    std::atomic<double> total_pnl_;
    std::atomic<double> sharpe_ratio_;
    std::atomic<double> max_drawdown_;
    std::atomic<double> win_rate_;
    
    // Rate limiting
    std::chrono::steady_clock::time_point last_request_time_;
    std::atomic<int> requests_count_;
    const int max_requests_per_second_ = 10;

public:
    TradingEngine();
    ~TradingEngine();
    
    // Core functionality
    void start();
    void stop();
    bool is_running() const;
    
    // Strategy management
    void add_strategy(std::unique_ptr<Strategy> strategy);
    void remove_strategy(const std::string& name);
    std::vector<std::string> get_strategy_names() const;
    
    // Market data handling
    void feed_market_data(const MarketData& data);
    void process_market_data();
    
    // Order management
    void submit_order(const Order& order);
    void process_orders();
    std::vector<Order> get_pending_orders();
    
    // Risk management
    void set_risk_parameters(double max_pos_size, double risk_limit,
                           double stop_loss, double take_profit);
    void run_risk_checks();
    
    // Performance metrics
    double get_total_pnl() const;
    double get_sharpe_ratio() const;
    double get_max_drawdown() const;
    double get_win_rate() const;
    
    // Rate limiting
    bool can_make_request();
    void record_request();
    
    // Utility functions
    std::string generate_order_id();
    double calculate_kelly_fraction(double win_prob, double win_loss_ratio);
    std::vector<double> calculate_portfolio_weights(
        const std::vector<std::string>& symbols,
        const std::vector<std::vector<double>>& returns);
};

// Arbitrage opportunity detector
class ArbitrageDetector {
private:
    std::unordered_map<std::string, std::vector<MarketData>> exchange_data_;
    double min_profit_threshold_;
    
public:
    ArbitrageDetector(double min_profit = 0.001);
    
    void update_market_data(const MarketData& data);
    std::vector<Order> detect_cross_exchange_arbitrage();
    std::vector<Order> detect_triangular_arbitrage();
    double calculate_funding_rate_arbitrage(const std::string& symbol);
};

// Statistical models
class StatisticalModels {
public:
    static double calculate_cointegration(const std::vector<double>& series1,
                                        const std::vector<double>& series2);
    static double calculate_half_life(const std::vector<double>& series);
    static std::vector<double> calculate_zscore(const std::vector<double>& series);
    static double calculate_volatility(const std::vector<double>& returns);
    static double calculate_correlation(const std::vector<double>& series1,
                                      const std::vector<double>& series2);
};

} // namespace sigma

#endif // SIGMA_ENGINE_HPP
