"""
Risk Management Models

Data models and classes for risk management calculations
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import numpy as np
from enum import Enum

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class RiskMetrics:
    """Overall portfolio risk metrics"""
    portfolio_value: float
    total_exposure: float
    net_exposure: float
    gross_exposure: float
    beta: float
    var_95: float
    var_99: float
    expected_shortfall: float
    sharpe_ratio: float
    max_drawdown: float
    volatility: float
    correlation_to_market: float
    concentration_risk: float
    liquidity_risk: float
    timestamp: datetime

@dataclass
class PositionRisk:
    """Individual position risk metrics"""
    symbol: str
    quantity: float
    market_value: float
    unrealized_pnl: float
    position_beta: float
    position_var: float
    concentration_percentage: float
    liquidity_score: float
    time_to_liquidate: float  # hours
    risk_level: RiskLevel
    stop_loss_price: Optional[float] = None
    take_profit_price: Optional[float] = None

@dataclass
class PortfolioRisk:
    """Portfolio-level risk assessment"""
    total_positions: int
    long_exposure: float
    short_exposure: float
    net_exposure: float
    sector_concentrations: Dict[str, float]
    currency_exposures: Dict[str, float]
    exchange_concentrations: Dict[str, float]
    correlation_matrix: np.ndarray
    risk_budget_utilization: float
    margin_utilization: float
    leverage_ratio: float

@dataclass
class VaRModel:
    """Value at Risk model configuration"""
    method: str  # 'historical', 'parametric', 'monte_carlo'
    confidence_level: float
    time_horizon: int  # days
    lookback_period: int  # days
    decay_factor: float  # for exponential weighting
    simulation_runs: int  # for Monte Carlo
    
@dataclass
class StressTestScenario:
    """Stress test scenario definition"""
    name: str
    description: str
    market_shocks: Dict[str, float]  # symbol -> shock percentage
    correlation_changes: Dict[Tuple[str, str], float]
    volatility_multipliers: Dict[str, float]
    liquidity_impacts: Dict[str, float]
    expected_pnl: float
    probability: float

@dataclass
class RiskLimit:
    """Risk limit definition"""
    limit_type: str  # 'position_size', 'sector_exposure', 'var', etc.
    symbol: Optional[str] = None
    sector: Optional[str] = None
    limit_value: float = 0.0
    current_value: float = 0.0
    utilization_percentage: float = 0.0
    is_breached: bool = False
    breach_timestamp: Optional[datetime] = None

@dataclass
class RiskAlert:
    """Risk alert/warning"""
    alert_id: str
    alert_type: str
    severity: RiskLevel
    message: str
    symbol: Optional[str] = None
    current_value: float = 0.0
    threshold_value: float = 0.0
    timestamp: datetime = datetime.now()
    acknowledged: bool = False

@dataclass
class KellyPosition:
    """Kelly criterion position sizing"""
    symbol: str
    win_probability: float
    win_loss_ratio: float
    kelly_fraction: float
    recommended_size: float
    max_size_limit: float
    final_position_size: float
    confidence_level: float

@dataclass
class CorrelationData:
    """Correlation analysis data"""
    symbol_pair: Tuple[str, str]
    correlation: float
    rolling_correlation: List[float]
    correlation_stability: float
    lookback_period: int
    last_updated: datetime

@dataclass
class LiquidityMetrics:
    """Liquidity assessment metrics"""
    symbol: str
    average_daily_volume: float
    bid_ask_spread: float
    market_impact: float
    time_to_liquidate: float
    liquidity_score: float  # 0-100
    exchange_liquidity: Dict[str, float]
    
@dataclass
class DrawdownAnalysis:
    """Drawdown analysis results"""
    current_drawdown: float
    max_drawdown: float
    max_drawdown_duration: int  # days
    recovery_time: int  # days
    drawdown_frequency: float
    average_drawdown: float
    drawdown_history: List[Tuple[datetime, float]]

@dataclass
class VolatilityMetrics:
    """Volatility analysis metrics"""
    symbol: str
    realized_volatility: float
    implied_volatility: Optional[float]
    volatility_percentile: float
    volatility_trend: str  # 'increasing', 'decreasing', 'stable'
    garch_forecast: Optional[float]
    volatility_regime: str  # 'low', 'medium', 'high'

@dataclass
class RiskBudget:
    """Risk budget allocation"""
    strategy_name: str
    allocated_var: float
    used_var: float
    utilization_percentage: float
    remaining_capacity: float
    performance_attribution: float
    risk_adjusted_return: float

class RiskCalculator:
    """Utility class for risk calculations"""
    
    @staticmethod
    def calculate_portfolio_beta(positions: List[PositionRisk], 
                               market_returns: np.ndarray) -> float:
        """Calculate portfolio beta"""
        if not positions:
            return 0.0
        
        total_value = sum(pos.market_value for pos in positions)
        if total_value == 0:
            return 0.0
        
        weighted_beta = sum(
            pos.position_beta * (pos.market_value / total_value)
            for pos in positions
        )
        
        return weighted_beta
    
    @staticmethod
    def calculate_concentration_risk(positions: List[PositionRisk]) -> float:
        """Calculate concentration risk using Herfindahl index"""
        if not positions:
            return 0.0
        
        total_value = sum(abs(pos.market_value) for pos in positions)
        if total_value == 0:
            return 0.0
        
        # Herfindahl-Hirschman Index
        hhi = sum(
            (abs(pos.market_value) / total_value) ** 2
            for pos in positions
        )
        
        return hhi
    
    @staticmethod
    def calculate_var_historical(returns: np.ndarray, 
                               confidence_level: float = 0.95) -> float:
        """Calculate historical VaR"""
        if len(returns) == 0:
            return 0.0
        
        percentile = (1 - confidence_level) * 100
        var = np.percentile(returns, percentile)
        return abs(var)
    
    @staticmethod
    def calculate_expected_shortfall(returns: np.ndarray,
                                   confidence_level: float = 0.95) -> float:
        """Calculate Expected Shortfall (Conditional VaR)"""
        if len(returns) == 0:
            return 0.0
        
        var = RiskCalculator.calculate_var_historical(returns, confidence_level)
        tail_returns = returns[returns <= -var]
        
        if len(tail_returns) == 0:
            return var
        
        return abs(np.mean(tail_returns))
    
    @staticmethod
    def calculate_kelly_fraction(win_prob: float, 
                               win_loss_ratio: float) -> float:
        """Calculate Kelly criterion fraction"""
        if win_loss_ratio <= 0:
            return 0.0
        
        kelly = (win_prob * win_loss_ratio - (1 - win_prob)) / win_loss_ratio
        return max(0.0, min(kelly, 0.25))  # Cap at 25% for safety
    
    @staticmethod
    def calculate_sharpe_ratio(returns: np.ndarray, 
                             risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns - risk_free_rate / 252  # Daily risk-free rate
        
        if np.std(excess_returns) == 0:
            return 0.0
        
        return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
    
    @staticmethod
    def calculate_max_drawdown(cumulative_returns: np.ndarray) -> Tuple[float, int]:
        """Calculate maximum drawdown and duration"""
        if len(cumulative_returns) == 0:
            return 0.0, 0
        
        peak = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - peak) / peak
        
        max_dd = np.min(drawdown)
        
        # Calculate duration
        max_dd_idx = np.argmin(drawdown)
        recovery_idx = max_dd_idx
        
        # Find recovery point
        for i in range(max_dd_idx + 1, len(cumulative_returns)):
            if cumulative_returns[i] >= peak[max_dd_idx]:
                recovery_idx = i
                break
        
        duration = recovery_idx - max_dd_idx
        
        return abs(max_dd), duration
