"""
Sigma Protocol API Module

FastAPI-based REST API for the Sigma Protocol trading system.
Provides endpoints for:
- Strategy management
- Risk monitoring
- Performance analytics
- System health checks
- Real-time data feeds
"""

from .app import create_app
from .routes import router
from .middleware import setup_middleware

__all__ = ['create_app', 'router', 'setup_middleware']

__version__ = '1.0.0'
__author__ = 'Hector <PERSON>'
