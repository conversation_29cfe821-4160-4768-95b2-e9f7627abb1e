# Environment Configuration
ENVIRONMENT=development
DEBUG=true

# Database Configuration
DATABASE_URL=postgresql://sigma_user:sigma_password@localhost:5432/sigma_protocol
POSTGRES_PASSWORD=sigma_password

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# API Keys - Exchange APIs
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key
COINBASE_API_KEY=your_coinbase_api_key
COINBASE_SECRET_KEY=your_coinbase_secret_key
KRAKEN_API_KEY=your_kraken_api_key
KRAKEN_SECRET_KEY=your_kraken_secret_key

# DEX Configuration
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_infura_key
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your_infura_key
ARBITRUM_RPC_URL=https://arbitrum-mainnet.infura.io/v3/your_infura_key
PRIVATE_KEY=your_ethereum_private_key

# MEV Configuration
FLASHBOTS_RELAY_URL=https://relay.flashbots.net
FLASHBOTS_PRIVATE_KEY=your_flashbots_private_key

# Security
JWT_SECRET_KEY=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Monitoring
PROMETHEUS_ENABLED=true
GRAFANA_PASSWORD=admin

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/sigma-protocol.log

# Trading Configuration
MAX_POSITION_SIZE=10000
RISK_LIMIT=0.02
STOP_LOSS_PERCENTAGE=0.05
TAKE_PROFIT_PERCENTAGE=0.10

# Strategy Configuration
PERPETUAL_ARBITRAGE_ENABLED=true
DEX_AMM_ENABLED=true
STATISTICAL_ARBITRAGE_ENABLED=true
MEV_INTEGRATION_ENABLED=true

# Performance Targets
TARGET_SHARPE_RATIO=1.5
MAX_DRAWDOWN=0.10
TARGET_BETA=0.1

# Cloud Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

GCP_PROJECT_ID=your_gcp_project_id
GCP_SERVICE_ACCOUNT_KEY=path/to/service-account-key.json

# Netlify Configuration
NETLIFY_SITE_ID=your_netlify_site_id
NETLIFY_ACCESS_TOKEN=your_netlify_access_token

# SEO/GEO Configuration
GEOIP_DATABASE_PATH=data/GeoLite2-City.mmdb
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,es,fr,de,ja,ko,zh

# Email Configuration (for alerts)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password

# Webhook Configuration
DISCORD_WEBHOOK_URL=your_discord_webhook_url
SLACK_WEBHOOK_URL=your_slack_webhook_url

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=3600
BACKUP_RETENTION_DAYS=30
