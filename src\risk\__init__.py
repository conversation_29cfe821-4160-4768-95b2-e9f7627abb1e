"""
Risk Management Module

This module provides comprehensive risk management capabilities including:
- Portfolio risk monitoring and limits
- Position sizing using Kelly criterion
- Market-neutral enforcement
- VaR and stress testing
- Real-time risk metrics
"""

from .manager import RiskManager
from .models import (
    RiskMetrics,
    PositionRisk,
    PortfolioRisk,
    VaRModel,
    StressTestScenario
)

__all__ = [
    'RiskManager',
    'RiskMetrics',
    'PositionRisk', 
    'PortfolioRisk',
    'VaRModel',
    'StressTestScenario'
]

__version__ = '1.0.0'
__author__ = 'Hector <PERSON>'
