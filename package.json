{"name": "sigma-protocol", "version": "1.0.0", "description": "Automated Market-Neutral Strategy Suite", "main": "src/web/dashboard.js", "scripts": {"build": "webpack --mode production", "dev": "webpack --mode development --watch", "start": "webpack-dev-server --mode development", "test": "jest", "lint": "eslint src/web/**/*.js", "format": "prettier --write src/web/**/*.{js,css,html}", "analyze": "webpack-bundle-analyzer dist/stats.json"}, "keywords": ["trading", "cryptocurrency", "arbitrage", "market-neutral", "defi", "mev", "algorithmic-trading"], "author": "<PERSON> <31132150+Hector<PERSON><EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/sigma-protocol.git"}, "dependencies": {"chart.js": "^4.4.0", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "moment": "^2.29.4", "lodash": "^4.17.21", "bootstrap": "^5.3.2"}, "devDependencies": {"webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-bundle-analyzer": "^4.10.1", "babel-loader": "^9.1.3", "@babel/core": "^7.23.5", "@babel/preset-env": "^7.23.5", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "html-webpack-plugin": "^5.5.3", "mini-css-extract-plugin": "^2.7.6", "terser-webpack-plugin": "^5.3.9", "css-minimizer-webpack-plugin": "^5.0.1", "eslint": "^8.55.0", "prettier": "^3.1.0", "jest": "^29.7.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}