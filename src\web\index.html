<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sigma Protocol - Automated Market-Neutral Cryptocurrency Trading System with advanced arbitrage strategies">
    <meta name="keywords" content="cryptocurrency trading, arbitrage, market neutral, defi, mev, algorithmic trading, quantitative finance">
    <meta name="author" content="Hector Ta">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://sigma-protocol.com/">
    <meta property="og:title" content="Sigma Protocol - Automated Market-Neutral Trading">
    <meta property="og:description" content="Advanced automated trading system with perpetual futures arbitrage, DEX AMM strategies, and MEV integration">
    <meta property="og:image" content="/assets/sigma-protocol-og.png">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sigma-protocol.com/">
    <meta property="twitter:title" content="Sigma Protocol - Automated Market-Neutral Trading">
    <meta property="twitter:description" content="Advanced automated trading system with perpetual futures arbitrage, DEX AMM strategies, and MEV integration">
    <meta property="twitter:image" content="/assets/sigma-protocol-twitter.png">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    
    <title>Sigma Protocol - Automated Market-Neutral Trading Dashboard</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Sigma Protocol",
        "description": "Automated Market-Neutral Cryptocurrency Trading System",
        "applicationCategory": "FinanceApplication",
        "operatingSystem": "Web",
        "author": {
            "@type": "Person",
            "name": "Hector Ta",
            "url": "https://github.com/HectorTa1989"
        },
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "featureList": [
            "Perpetual Futures Arbitrage",
            "DEX AMM Strategies",
            "Statistical Arbitrage", 
            "MEV Integration",
            "Risk Management",
            "Real-time Monitoring"
        ]
    }
    </script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>
                Sigma Protocol
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#dashboard">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#strategies">Strategies</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#risk">Risk</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#performance">Performance</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid mt-5 pt-3">
        <!-- System Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-dark text-white">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <div class="status-indicator me-3" id="systemStatus"></div>
                                    <div>
                                        <h6 class="mb-0">System Status</h6>
                                        <small class="text-muted" id="systemStatusText">Initializing...</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <h6 class="mb-0">Total P&L</h6>
                                <h4 class="mb-0 text-success" id="totalPnL">$0.00</h4>
                            </div>
                            <div class="col-md-3">
                                <h6 class="mb-0">Sharpe Ratio</h6>
                                <h4 class="mb-0" id="sharpeRatio">0.00</h4>
                            </div>
                            <div class="col-md-3">
                                <h6 class="mb-0">Market Beta</h6>
                                <h4 class="mb-0" id="marketBeta">0.00</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Section -->
        <section id="dashboard" class="mb-5">
            <h2 class="mb-4">Trading Dashboard</h2>
            
            <!-- Performance Charts -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Portfolio Performance</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="performanceChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Strategy Allocation</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="allocationChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Metrics -->
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-coins fa-2x text-primary mb-2"></i>
                            <h5>Active Positions</h5>
                            <h3 id="activePositions">0</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-exchange-alt fa-2x text-success mb-2"></i>
                            <h5>Daily Trades</h5>
                            <h3 id="dailyTrades">0</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-percentage fa-2x text-warning mb-2"></i>
                            <h5>Win Rate</h5>
                            <h3 id="winRate">0%</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-shield-alt fa-2x text-info mb-2"></i>
                            <h5>Max Drawdown</h5>
                            <h3 id="maxDrawdown">0%</h3>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Strategies Section -->
        <section id="strategies" class="mb-5">
            <h2 class="mb-4">Trading Strategies</h2>
            <div class="row" id="strategiesContainer">
                <!-- Strategy cards will be populated by JavaScript -->
            </div>
        </section>

        <!-- Risk Management Section -->
        <section id="risk" class="mb-5">
            <h2 class="mb-4">Risk Management</h2>
            <div class="row">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Risk Metrics</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <tbody id="riskMetricsTable">
                                        <!-- Risk metrics will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Position Exposure</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="exposureChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Performance Section -->
        <section id="performance" class="mb-5">
            <h2 class="mb-4">Performance Analytics</h2>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Trades</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Time</th>
                                            <th>Strategy</th>
                                            <th>Symbol</th>
                                            <th>Side</th>
                                            <th>Quantity</th>
                                            <th>Price</th>
                                            <th>P&L</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tradesTable">
                                        <!-- Trades will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Sigma Protocol</h5>
                    <p class="mb-0">Automated Market-Neutral Trading System</p>
                    <small class="text-muted">Built by <a href="https://github.com/HectorTa1989" class="text-decoration-none">Hector Ta</a></small>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="mb-2">
                        <a href="https://github.com/HectorTa1989/sigma-protocol" class="text-white me-3">
                            <i class="fab fa-github"></i> GitHub
                        </a>
                        <a href="/docs" class="text-white me-3">
                            <i class="fas fa-book"></i> API Docs
                        </a>
                        <a href="/health" class="text-white">
                            <i class="fas fa-heartbeat"></i> Health
                        </a>
                    </div>
                    <small class="text-muted">© 2024 Sigma Protocol. MIT License.</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/socket.io-client@4.7.4/dist/socket.io.min.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>
