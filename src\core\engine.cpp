#include "engine.hpp"
#include <algorithm>
#include <random>
#include <cmath>
#include <numeric>
#include <sstream>
#include <iomanip>

namespace sigma {

// RiskManager implementation
RiskManager::RiskManager(double max_pos_size, double risk_limit, 
                        double stop_loss, double take_profit)
    : max_position_size_(max_pos_size), risk_limit_(risk_limit),
      stop_loss_percentage_(stop_loss), take_profit_percentage_(take_profit) {}

bool RiskManager::validate_order(const Order& order) {
    std::lock_guard<std::mutex> lock(positions_mutex_);
    
    // Check position size limits
    auto it = positions_.find(order.symbol);
    double current_position = (it != positions_.end()) ? it->second.quantity : 0.0;
    double new_position = current_position + 
        (order.side == "buy" ? order.quantity : -order.quantity);
    
    if (std::abs(new_position) > max_position_size_) {
        return false;
    }
    
    // Check portfolio risk
    double portfolio_beta = calculate_portfolio_beta();
    if (std::abs(portfolio_beta) > 0.1) { // Market neutral requirement
        return false;
    }
    
    return true;
}

void RiskManager::update_position(const std::string& symbol, const Order& order) {
    std::lock_guard<std::mutex> lock(positions_mutex_);
    
    auto& position = positions_[symbol];
    position.symbol = symbol;
    position.exchange = order.exchange;
    
    if (order.side == "buy") {
        double total_cost = position.quantity * position.avg_price + 
                           order.quantity * order.price;
        position.quantity += order.quantity;
        position.avg_price = total_cost / position.quantity;
    } else {
        position.quantity -= order.quantity;
        if (position.quantity == 0) {
            positions_.erase(symbol);
        }
    }
}

double RiskManager::calculate_portfolio_beta() {
    // Simplified beta calculation - in practice, use market data
    double total_exposure = 0.0;
    for (const auto& [symbol, position] : positions_) {
        total_exposure += std::abs(position.quantity * position.avg_price);
    }
    return total_exposure > 0 ? 0.05 : 0.0; // Placeholder
}

// TradingEngine implementation
TradingEngine::TradingEngine() 
    : running_(false), total_pnl_(0.0), sharpe_ratio_(0.0), 
      max_drawdown_(0.0), win_rate_(0.0), requests_count_(0) {
    
    risk_manager_ = std::make_unique<RiskManager>(10000.0, 0.02, 0.05, 0.10);
}

TradingEngine::~TradingEngine() {
    stop();
}

void TradingEngine::start() {
    if (running_.load()) return;
    
    running_.store(true);
    
    // Start processing threads
    market_data_thread_ = std::thread(&TradingEngine::process_market_data, this);
    execution_thread_ = std::thread(&TradingEngine::process_orders, this);
    risk_thread_ = std::thread(&TradingEngine::run_risk_checks, this);
}

void TradingEngine::stop() {
    running_.store(false);
    
    if (market_data_thread_.joinable()) market_data_thread_.join();
    if (execution_thread_.joinable()) execution_thread_.join();
    if (risk_thread_.joinable()) risk_thread_.join();
}

bool TradingEngine::is_running() const {
    return running_.load();
}

void TradingEngine::add_strategy(std::unique_ptr<Strategy> strategy) {
    strategies_.push_back(std::move(strategy));
}

void TradingEngine::feed_market_data(const MarketData& data) {
    std::lock_guard<std::mutex> lock(market_data_mutex_);
    market_data_queue_.push(data);
}

void TradingEngine::process_market_data() {
    while (running_.load()) {
        std::unique_lock<std::mutex> lock(market_data_mutex_);
        
        if (!market_data_queue_.empty()) {
            MarketData data = market_data_queue_.front();
            market_data_queue_.pop();
            lock.unlock();
            
            // Feed data to all strategies
            for (auto& strategy : strategies_) {
                if (strategy->is_enabled()) {
                    strategy->on_market_data(data);
                    
                    // Generate signals
                    auto signals = strategy->generate_signals();
                    for (const auto& order : signals) {
                        submit_order(order);
                    }
                }
            }
        } else {
            lock.unlock();
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
}

void TradingEngine::submit_order(const Order& order) {
    if (risk_manager_->validate_order(order)) {
        std::lock_guard<std::mutex> lock(orders_mutex_);
        order_queue_.push(order);
    }
}

void TradingEngine::process_orders() {
    while (running_.load()) {
        std::unique_lock<std::mutex> lock(orders_mutex_);
        
        if (!order_queue_.empty()) {
            Order order = order_queue_.front();
            order_queue_.pop();
            lock.unlock();
            
            // Rate limiting check
            if (!can_make_request()) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                continue;
            }
            
            // Simulate order execution (replace with actual exchange API calls)
            order.status = "filled";
            order.updated_at = std::chrono::system_clock::now();
            
            // Update position
            risk_manager_->update_position(order.symbol, order);
            
            record_request();
        } else {
            lock.unlock();
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
}

void TradingEngine::run_risk_checks() {
    while (running_.load()) {
        auto risk_orders = risk_manager_->generate_risk_orders();
        for (const auto& order : risk_orders) {
            submit_order(order);
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

bool TradingEngine::can_make_request() {
    auto now = std::chrono::steady_clock::now();
    auto time_diff = std::chrono::duration_cast<std::chrono::seconds>(
        now - last_request_time_).count();
    
    if (time_diff >= 1) {
        requests_count_.store(0);
        last_request_time_ = now;
    }
    
    return requests_count_.load() < max_requests_per_second_;
}

void TradingEngine::record_request() {
    requests_count_.fetch_add(1);
}

std::string TradingEngine::generate_order_id() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(100000, 999999);
    
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()).count();
    
    std::stringstream ss;
    ss << "ORD_" << timestamp << "_" << dis(gen);
    return ss.str();
}

double TradingEngine::calculate_kelly_fraction(double win_prob, double win_loss_ratio) {
    return (win_prob * win_loss_ratio - (1 - win_prob)) / win_loss_ratio;
}

// ArbitrageDetector implementation
ArbitrageDetector::ArbitrageDetector(double min_profit) 
    : min_profit_threshold_(min_profit) {}

void ArbitrageDetector::update_market_data(const MarketData& data) {
    exchange_data_[data.exchange].push_back(data);
    
    // Keep only recent data (last 100 updates per exchange)
    if (exchange_data_[data.exchange].size() > 100) {
        exchange_data_[data.exchange].erase(
            exchange_data_[data.exchange].begin());
    }
}

std::vector<Order> ArbitrageDetector::detect_cross_exchange_arbitrage() {
    std::vector<Order> orders;
    
    // Find arbitrage opportunities between exchanges
    for (const auto& [exchange1, data1] : exchange_data_) {
        for (const auto& [exchange2, data2] : exchange_data_) {
            if (exchange1 >= exchange2 || data1.empty() || data2.empty()) continue;
            
            const auto& latest1 = data1.back();
            const auto& latest2 = data2.back();
            
            if (latest1.symbol != latest2.symbol) continue;
            
            // Check for arbitrage opportunity
            double profit_margin = (latest2.bid - latest1.ask) / latest1.ask;
            
            if (profit_margin > min_profit_threshold_) {
                // Buy on exchange1, sell on exchange2
                Order buy_order;
                buy_order.id = "ARB_BUY_" + std::to_string(std::time(nullptr));
                buy_order.symbol = latest1.symbol;
                buy_order.side = "buy";
                buy_order.quantity = 1.0; // Simplified
                buy_order.price = latest1.ask;
                buy_order.type = "limit";
                buy_order.exchange = exchange1;
                
                Order sell_order;
                sell_order.id = "ARB_SELL_" + std::to_string(std::time(nullptr));
                sell_order.symbol = latest2.symbol;
                sell_order.side = "sell";
                sell_order.quantity = 1.0;
                sell_order.price = latest2.bid;
                sell_order.type = "limit";
                sell_order.exchange = exchange2;
                
                orders.push_back(buy_order);
                orders.push_back(sell_order);
            }
        }
    }
    
    return orders;
}

// StatisticalModels implementation
double StatisticalModels::calculate_cointegration(
    const std::vector<double>& series1, const std::vector<double>& series2) {
    
    if (series1.size() != series2.size() || series1.empty()) {
        return 0.0;
    }
    
    // Simplified cointegration test (Engle-Granger)
    double correlation = calculate_correlation(series1, series2);
    return correlation; // Placeholder - implement proper cointegration test
}

double StatisticalModels::calculate_correlation(
    const std::vector<double>& series1, const std::vector<double>& series2) {
    
    if (series1.size() != series2.size() || series1.empty()) {
        return 0.0;
    }
    
    double mean1 = std::accumulate(series1.begin(), series1.end(), 0.0) / series1.size();
    double mean2 = std::accumulate(series2.begin(), series2.end(), 0.0) / series2.size();
    
    double numerator = 0.0, sum_sq1 = 0.0, sum_sq2 = 0.0;
    
    for (size_t i = 0; i < series1.size(); ++i) {
        double diff1 = series1[i] - mean1;
        double diff2 = series2[i] - mean2;
        numerator += diff1 * diff2;
        sum_sq1 += diff1 * diff1;
        sum_sq2 += diff2 * diff2;
    }
    
    double denominator = std::sqrt(sum_sq1 * sum_sq2);
    return denominator > 0 ? numerator / denominator : 0.0;
}

} // namespace sigma
