# Sigma Protocol Core Engine Makefile

CXX = g++
CXXFLAGS = -std=c++17 -O3 -Wall -Wextra -fPIC
INCLUDES = -I/usr/include/python3.11 -I$(shell python3 -m pybind11 --includes)
LDFLAGS = -shared -lpthread

# Source files
SOURCES = engine.cpp bindings.cpp
OBJECTS = $(SOURCES:.cpp=.o)
TARGET = sigma_core$(shell python3-config --extension-suffix)

# Default target
all: $(TARGET)

# Build the shared library
$(TARGET): $(OBJECTS)
	$(CXX) $(LDFLAGS) -o $@ $^

# Compile source files
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Clean build artifacts
clean:
	rm -f $(OBJECTS) $(TARGET)

# Install dependencies
install-deps:
	sudo apt-get update
	sudo apt-get install -y build-essential cmake python3-dev
	pip3 install pybind11

# Build for development
dev: CXXFLAGS += -g -DDEBUG
dev: $(TARGET)

# Build for production
prod: CXXFLAGS += -DNDEBUG -march=native
prod: $(TARGET)

# Run tests
test: $(TARGET)
	python3 -c "import sigma_core; print('Core engine loaded successfully')"

# Format code
format:
	clang-format -i *.cpp *.hpp

# Static analysis
analyze:
	cppcheck --enable=all --std=c++17 *.cpp *.hpp

# Profile build
profile: CXXFLAGS += -pg
profile: $(TARGET)

# Memory check
memcheck: $(TARGET)
	valgrind --tool=memcheck --leak-check=full python3 -c "import sigma_core"

# Benchmark
benchmark: $(TARGET)
	python3 -c "
import sigma_core
import time
engine = sigma_core.TradingEngine()
start = time.time()
for i in range(10000):
    engine.generate_order_id()
print(f'Generated 10000 order IDs in {time.time() - start:.4f} seconds')
"

# Help
help:
	@echo "Available targets:"
	@echo "  all        - Build the core engine (default)"
	@echo "  dev        - Build with debug symbols"
	@echo "  prod       - Build optimized for production"
	@echo "  clean      - Remove build artifacts"
	@echo "  test       - Test the built module"
	@echo "  format     - Format source code"
	@echo "  analyze    - Run static analysis"
	@echo "  profile    - Build with profiling support"
	@echo "  memcheck   - Run memory leak detection"
	@echo "  benchmark  - Run performance benchmark"
	@echo "  help       - Show this help message"

.PHONY: all clean install-deps dev prod test format analyze profile memcheck benchmark help
